import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PrefsService {
  late SharedPreferences prefs;
  late Future<void> _prefsInitFuture;

  PrefsService() {
    _prefsInitFuture = _initPrefs();
  }

  Future<void> _initPrefs() async {
    prefs = await SharedPreferences.getInstance();
  }

  /// Clear user data when logged out
  Future<void> clearUserData() async {
    await _prefsInitFuture;
    // Remove user ID
    await prefs.remove(_PrefsKeys.uid);
    // Remove navigation bar visibility setting
    await prefs.remove(_PrefsKeys.shouldShowNavBar);
    // Keep device connection data if needed for future logins or clear it
    await prefs.remove(_PrefsKeys.lastConnectedDeviceMac);
    await prefs.remove(_PrefsKeys.lastConnectedDeviceName);

    // Clear API call tracking data
    await prefs.remove(_PrefsKeys.lastAmbeeApiCalledAt);
    await prefs.remove(_PrefsKeys.todaysBeforeNoonAmbeeApiCallCount);
    await prefs.remove(_PrefsKeys.todaysAfterNoonAmbeeApiCallCount);

    // Remove FCM token
    await prefs.remove(_PrefsKeys.fcmToken);

    // Reset first connection flag if needed
    await prefs.remove(_PrefsKeys.isRingFirstConnection);
    debugPrint("User preferences data cleared");
  }

  Future<String> getUid() async {
    await _prefsInitFuture;
    String? uid = prefs.getString(_PrefsKeys.uid);
    return uid ?? "";
  }

  Future<void> setUid(String uid) async {
    await prefs.setString(_PrefsKeys.uid, uid);
  }

  Future<String> getLastConnectedDeviceMac() async {
    await _prefsInitFuture;
    String? lastConnectedDeviceMac =
        prefs.getString(_PrefsKeys.lastConnectedDeviceMac);
    return lastConnectedDeviceMac ?? "";
  }

  Future<void> setLastConnectedDeviceMac(String lastConnectedDeviceMac) async {
    await prefs.setString(
        _PrefsKeys.lastConnectedDeviceMac, lastConnectedDeviceMac);
  }

  String getLastConnectedDeviceName() {
    String? lastConnectedDeviceName =
        prefs.getString(_PrefsKeys.lastConnectedDeviceName);
    return lastConnectedDeviceName ?? "";
  }

  Future<void> setLastConnectedDeviceName(
      String lastConnectedDeviceName) async {
    await prefs.setString(
        _PrefsKeys.lastConnectedDeviceName, lastConnectedDeviceName);
  }

  Future<int> getLastAmbeeApiCalledAt() async {
    await _prefsInitFuture;
    int? lastAmbeeApiCalledAt = prefs.getInt(_PrefsKeys.lastAmbeeApiCalledAt);
    return lastAmbeeApiCalledAt ?? 0;
  }

  Future<void> setLastAmbeeApiCalledAt(int lastAmbeeApiCalledAt) async {
    await prefs.setInt(_PrefsKeys.lastAmbeeApiCalledAt, lastAmbeeApiCalledAt);
  }

  Future<int> getTodaysBeforeNoonAmbeeApiCallCount() async {
    await _prefsInitFuture;
    int? todaysBeforeNoonAmbeeApiCallCount =
        prefs.getInt(_PrefsKeys.todaysBeforeNoonAmbeeApiCallCount);
    return todaysBeforeNoonAmbeeApiCallCount ?? 0;
  }

  Future<void> setTodaysBeforeNoonAmbeeApiCallCount(
      int todaysBeforeNoonAmbeeApiCallCount) async {
    await prefs.setInt(_PrefsKeys.todaysBeforeNoonAmbeeApiCallCount,
        todaysBeforeNoonAmbeeApiCallCount);
  }

  Future<int> getTodaysAfterNoonAmbeeApiCallCount() async {
    await _prefsInitFuture;
    int? todaysAfterNoonAmbeeApiCallCount =
        prefs.getInt(_PrefsKeys.todaysAfterNoonAmbeeApiCallCount);
    return todaysAfterNoonAmbeeApiCallCount ?? 0;
  }

  Future<void> setTodaysAfterNoonAmbeeApiCallCount(
      int todaysAfterNoonAmbeeApiCallCount) async {
    await prefs.setInt(_PrefsKeys.todaysAfterNoonAmbeeApiCallCount,
        todaysAfterNoonAmbeeApiCallCount);
  }

  Future<String> getFcmToken() async {
    await _prefsInitFuture;
    String? fcmToken = prefs.getString(_PrefsKeys.fcmToken);
    return fcmToken ?? "";
  }

  Future<void> setFcmToken(String fcmToken) async {
    await prefs.setString(_PrefsKeys.fcmToken, fcmToken);
  }

  Future<void> clearFcmToken() async {
    await _prefsInitFuture;
    await prefs.remove(_PrefsKeys.fcmToken);
    debugPrint("FCM token cleared from preferences");
  }

  Future<bool> getIsRingFirstConnection() async {
    await _prefsInitFuture;
    bool? connection = prefs.getBool(_PrefsKeys.isRingFirstConnection);
    return connection ?? false;
  }

  Future<void> setIsRingFirstConnection(bool connection) async {
    await prefs.setBool(_PrefsKeys.isRingFirstConnection, connection);
  }

  Future<bool> getShouldShowNavbar() async {
    await _prefsInitFuture;
    bool? visibility = prefs.getBool(_PrefsKeys.shouldShowNavBar);
    return visibility ?? false;
  }

  Future<void> setShouldShowNavbar(bool visibility) async {
    await prefs.setBool(_PrefsKeys.shouldShowNavBar, visibility);
  }

  Future<bool> getHealthPermissionChecked() async {
    await _prefsInitFuture;
    bool? checked = prefs.getBool(_PrefsKeys.healthPermissionChecked);
    return checked ?? false;
  }

  Future<void> setHealthPermissionChecked(bool checked) async {
    await prefs.setBool(_PrefsKeys.healthPermissionChecked, checked);
  }

  Future<bool> getNotificationPermissionDialogShown() async {
    await _prefsInitFuture;
    bool? shown = prefs.getBool(_PrefsKeys.notificationPermissionDialogShown);
    return shown ?? false;
  }

  Future<void> setNotificationPermissionDialogShown(bool shown) async {
    await prefs.setBool(_PrefsKeys.notificationPermissionDialogShown, shown);
  }

  Future<void> setLastVoiceRecordingTimestamp(int timestamp) async {
    await prefs.setInt(_PrefsKeys.lastVoiceRecordingTimestampKey, timestamp);
  }

  Future<int?> getLastVoiceRecordingTimestamp() async {
    return prefs.getInt(_PrefsKeys.lastVoiceRecordingTimestampKey);
  }
}

class _PrefsKeys {
  static const String uid = "uid";
  static const String lastConnectedDeviceMac = "lastConnectedDeviceMac";
  static const String lastConnectedDeviceName = "lastConnectedDeviceName";
  static const String lastAmbeeApiCalledAt = "lastAmbeeApiCalledAt";
  static const String todaysBeforeNoonAmbeeApiCallCount =
      "todaysBeforeNoonAmbeeApiCallCount";
  static const String todaysAfterNoonAmbeeApiCallCount =
      "todaysAfterNoonAmbeeApiCallCount";
  static const String fcmToken = "fcmToken";
  static const String isRingFirstConnection = "isRingFirstConnection";
  static const String shouldShowNavBar = "shouldShowNavBar";
  static const String healthPermissionChecked = "healthPermissionChecked";
  static const String notificationPermissionDialogShown =
      "notificationPermissionDialogShown";
  static const String lastVoiceRecordingTimestampKey =
      'last_voice_recording_timestamp';
}
